/**
 * 测试GraphQL枚举值传递
 * 验证RefundReason枚举是否正确传递给服务器
 */

import { gql } from '@apollo/client'
import { REFUND_REASON } from '../utilities/orderEnums'
import Logger from '../core/logger'

const logger = new Logger('TestGraphQLEnum')

/**
 * 测试枚举值的格式
 */
export function testEnumValues() {
  logger.info('🧪 Testing enum values format...')
  
  // 检查枚举值
  Object.entries(REFUND_REASON).forEach(([key, value]) => {
    logger.debug(`Enum ${key}:`, { value, type: typeof value })
  })
  
  // 测试我们使用的值
  const testValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  logger.info('Test enum value', { 
    value: testValue, 
    type: typeof testValue,
    isString: typeof testValue === 'string'
  })
  
  return testValue
}

/**
 * 创建测试mutation来验证枚举值
 */
export function createTestMutation() {
  logger.info('🧪 Creating test mutation...')
  
  // 这是我们当前的mutation
  const currentMutation = gql`
    mutation CancelOrder($_id: String!, $reason: RefundReason!) {
      cancelOrder(_id: $_id, reason: $reason) {
        _id
        orderStatus
      }
    }
  `
  
  logger.debug('Current mutation created', { 
    mutation: currentMutation.loc.source.body 
  })
  
  return currentMutation
}

/**
 * 测试变量对象的构造
 */
export function testVariablesConstruction(orderId) {
  logger.info('🧪 Testing variables construction...')
  
  const testOrderId = orderId || 'test_order_id'
  const reason = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  
  // 构造变量对象
  const variables = {
    _id: testOrderId,
    reason: reason
  }
  
  logger.info('Variables object', { 
    variables,
    reasonType: typeof variables.reason,
    reasonValue: variables.reason
  })
  
  // 检查JSON序列化后的结果
  const serialized = JSON.stringify(variables)
  logger.debug('Serialized variables', { serialized })
  
  return variables
}

/**
 * 模拟Apollo Client发送请求的过程
 */
export function simulateApolloRequest(orderId) {
  logger.info('🧪 Simulating Apollo Client request...')
  
  try {
    const mutation = createTestMutation()
    const variables = testVariablesConstruction(orderId)
    
    // 模拟Apollo Client的请求构造
    const request = {
      query: mutation,
      variables: variables,
      operationName: 'CancelOrder'
    }
    
    logger.info('Simulated request', { 
      operationName: request.operationName,
      variables: request.variables,
      hasQuery: !!request.query
    })
    
    // 检查变量是否符合GraphQL规范
    const isValidEnum = typeof variables.reason === 'string' && 
                       Object.values(REFUND_REASON).includes(variables.reason)
    
    logger.info('Validation result', { 
      isValidEnum,
      reasonValue: variables.reason,
      availableValues: Object.values(REFUND_REASON)
    })
    
    return { request, isValidEnum }
  } catch (error) {
    logger.error('Simulation failed', { error: error.message })
    return { error }
  }
}

/**
 * 检查可能的枚举值问题
 */
export function diagnoseEnumIssues() {
  logger.info('🔍 Diagnosing potential enum issues...')
  
  const issues = []
  
  // 检查1: 枚举值是否为字符串
  const reasonValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  if (typeof reasonValue !== 'string') {
    issues.push('Enum value is not a string')
  }
  
  // 检查2: 枚举值是否包含特殊字符
  if (reasonValue.includes(' ') || reasonValue.includes('-')) {
    issues.push('Enum value contains spaces or hyphens')
  }
  
  // 检查3: 枚举值是否全大写
  if (reasonValue !== reasonValue.toUpperCase()) {
    issues.push('Enum value is not all uppercase')
  }
  
  // 检查4: 枚举值是否与键名匹配
  const expectedKey = 'MERCHANT_OUT_OF_STOCK'
  if (reasonValue !== expectedKey) {
    issues.push(`Enum value (${reasonValue}) does not match key (${expectedKey})`)
  }
  
  logger.info('Diagnosis complete', { 
    issues,
    hasIssues: issues.length > 0,
    enumValue: reasonValue
  })
  
  return { issues, enumValue: reasonValue }
}

/**
 * 运行所有枚举测试
 */
export function runAllEnumTests(orderId = 'test_order_123') {
  logger.info('🚀 Running all enum tests...')
  
  const enumTest = testEnumValues()
  const mutationTest = createTestMutation()
  const variablesTest = testVariablesConstruction(orderId)
  const simulationTest = simulateApolloRequest(orderId)
  const diagnosisTest = diagnoseEnumIssues()
  
  const summary = {
    enumValue: enumTest,
    mutationCreated: !!mutationTest,
    variablesValid: !!variablesTest,
    simulationSuccess: !simulationTest.error,
    issuesFound: diagnosisTest.issues.length,
    diagnosis: diagnosisTest
  }
  
  logger.info('🎯 All enum tests completed', summary)
  
  if (diagnosisTest.issues.length > 0) {
    logger.warn('⚠️ Issues found with enum handling', { 
      issues: diagnosisTest.issues 
    })
  } else {
    logger.info('✅ No enum issues detected')
  }
  
  return summary
}

export default {
  testEnumValues,
  createTestMutation,
  testVariablesConstruction,
  simulateApolloRequest,
  diagnoseEnumIssues,
  runAllEnumTests
}
