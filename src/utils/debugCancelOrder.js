/**
 * 调试取消订单问题
 * 专门用于诊断RefundReason枚举值传递问题
 */

import { gql } from '@apollo/client'
import { REFUND_REASON } from '../utilities/orderEnums'
import { cancelOrder as cancelOrderMutation } from '../apollo/mutations'
import Logger from '../core/logger'

const logger = new Logger('DebugCancelOrder')

/**
 * 调试枚举值传递
 */
export function debugEnumPassing() {
  logger.info('🔍 Debugging enum value passing...')
  
  // 检查枚举值
  const reasonValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  logger.info('Enum value details', {
    value: reasonValue,
    type: typeof reasonValue,
    stringified: JSON.stringify(reasonValue),
    length: reasonValue.length,
    charCodes: reasonValue.split('').map(c => c.charCodeAt(0))
  })
  
  // 检查所有枚举值
  logger.info('All enum values', {
    enumValues: REFUND_REASON,
    keys: Object.keys(REFUND_REASON),
    values: Object.values(REFUND_REASON)
  })
  
  return reasonValue
}

/**
 * 调试mutation字符串
 */
export function debugMutationString() {
  logger.info('🔍 Debugging mutation string...')
  
  logger.info('Mutation string', {
    mutation: cancelOrderMutation,
    length: cancelOrderMutation.length,
    includesRefundReason: cancelOrderMutation.includes('RefundReason'),
    includesString: cancelOrderMutation.includes('String')
  })
  
  // 检查是否有隐藏字符
  const cleanMutation = cancelOrderMutation.replace(/\s+/g, ' ').trim()
  logger.info('Cleaned mutation', {
    cleanMutation,
    original: cancelOrderMutation
  })
  
  return cancelOrderMutation
}

/**
 * 调试GraphQL解析
 */
export function debugGraphQLParsing() {
  logger.info('🔍 Debugging GraphQL parsing...')
  
  try {
    const parsedMutation = gql`${cancelOrderMutation}`
    
    logger.info('GraphQL parsing successful', {
      kind: parsedMutation.kind,
      definitions: parsedMutation.definitions.length,
      operationType: parsedMutation.definitions[0]?.operation,
      operationName: parsedMutation.definitions[0]?.name?.value
    })
    
    // 检查变量定义
    const variableDefinitions = parsedMutation.definitions[0]?.variableDefinitions
    if (variableDefinitions) {
      variableDefinitions.forEach((varDef, index) => {
        logger.info(`Variable ${index}`, {
          name: varDef.variable.name.value,
          type: varDef.type.kind === 'NonNullType' ? 
                `${varDef.type.type.name.value}!` : 
                varDef.type.name.value
        })
      })
    }
    
    return parsedMutation
  } catch (error) {
    logger.error('GraphQL parsing failed', {
      error: error.message,
      stack: error.stack
    })
    return null
  }
}

/**
 * 调试变量构造
 */
export function debugVariableConstruction(orderId = 'test_order_123') {
  logger.info('🔍 Debugging variable construction...')
  
  const reasonValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  
  // 构造变量对象
  const variables = {
    _id: orderId,
    reason: reasonValue
  }
  
  logger.info('Variables constructed', {
    variables,
    variableTypes: {
      _id: typeof variables._id,
      reason: typeof variables.reason
    },
    serialized: JSON.stringify(variables),
    reasonIsEnum: Object.values(REFUND_REASON).includes(variables.reason)
  })
  
  // 测试不同的枚举值传递方式
  const testVariations = [
    { name: 'direct_string', value: 'MERCHANT_OUT_OF_STOCK' },
    { name: 'enum_constant', value: REFUND_REASON.MERCHANT_OUT_OF_STOCK },
    { name: 'object_property', value: REFUND_REASON['MERCHANT_OUT_OF_STOCK'] }
  ]
  
  testVariations.forEach(variation => {
    logger.info(`Testing ${variation.name}`, {
      value: variation.value,
      type: typeof variation.value,
      equals: variation.value === 'MERCHANT_OUT_OF_STOCK',
      serialized: JSON.stringify(variation.value)
    })
  })
  
  return variables
}

/**
 * 模拟完整的请求
 */
export function simulateFullRequest(orderId = 'test_order_123') {
  logger.info('🔍 Simulating full request...')
  
  try {
    const mutation = debugGraphQLParsing()
    const variables = debugVariableConstruction(orderId)
    
    if (!mutation) {
      logger.error('Cannot simulate request - mutation parsing failed')
      return null
    }
    
    const request = {
      query: mutation,
      variables: variables,
      operationName: 'CancelOrder'
    }
    
    logger.info('Full request simulation', {
      hasQuery: !!request.query,
      hasVariables: !!request.variables,
      operationName: request.operationName,
      variableCount: Object.keys(request.variables).length
    })
    
    // 模拟Apollo Client的序列化过程
    const serializedRequest = {
      query: request.query.loc.source.body,
      variables: request.variables,
      operationName: request.operationName
    }
    
    logger.info('Serialized request', {
      queryLength: serializedRequest.query.length,
      variables: serializedRequest.variables,
      operationName: serializedRequest.operationName
    })
    
    return serializedRequest
  } catch (error) {
    logger.error('Request simulation failed', {
      error: error.message,
      stack: error.stack
    })
    return null
  }
}

/**
 * 运行所有调试测试
 */
export function runAllDebugTests(orderId = 'test_order_123') {
  logger.info('🚀 Running all debug tests for cancel order...')
  
  const enumResult = debugEnumPassing()
  const mutationResult = debugMutationString()
  const parsingResult = debugGraphQLParsing()
  const variablesResult = debugVariableConstruction(orderId)
  const requestResult = simulateFullRequest(orderId)
  
  const summary = {
    enumValue: enumResult,
    mutationValid: !!mutationResult,
    parsingSuccessful: !!parsingResult,
    variablesValid: !!variablesResult,
    requestSimulated: !!requestResult,
    allTestsPassed: !!(enumResult && mutationResult && parsingResult && variablesResult && requestResult)
  }
  
  logger.info('🎯 Debug tests summary', summary)
  
  if (summary.allTestsPassed) {
    logger.info('✅ All debug tests passed - the issue might be elsewhere')
  } else {
    logger.warn('⚠️ Some debug tests failed - found potential issues')
  }
  
  return summary
}

/**
 * 检查可能的问题源
 */
export function checkPotentialIssues() {
  logger.info('🔍 Checking potential issues...')
  
  const issues = []
  
  // 检查1: 枚举值格式
  const reasonValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  if (typeof reasonValue !== 'string') {
    issues.push('Enum value is not a string')
  }
  
  // 检查2: mutation字符串格式
  if (!cancelOrderMutation.includes('RefundReason!')) {
    issues.push('Mutation does not include RefundReason! type')
  }
  
  // 检查3: 是否有缓存问题
  if (cancelOrderMutation.includes('String!') && cancelOrderMutation.includes('RefundReason!')) {
    issues.push('Mutation contains both String! and RefundReason! - possible cache issue')
  }
  
  logger.info('Potential issues check', {
    issues,
    issueCount: issues.length,
    hasIssues: issues.length > 0
  })
  
  return issues
}

export default {
  debugEnumPassing,
  debugMutationString,
  debugGraphQLParsing,
  debugVariableConstruction,
  simulateFullRequest,
  runAllDebugTests,
  checkPotentialIssues
}
