/**
 * 清理各种缓存的工具
 * 用于解决GraphQL schema不一致等问题
 */

import AsyncStorage from '@react-native-async-storage/async-storage'
import * as SecureStore from 'expo-secure-store'
import { clientRef } from '../apollo/client'
import Logger from '../core/logger'

const logger = new Logger('ClearCaches')

/**
 * 清理Apollo Client缓存
 */
export async function clearApolloCache() {
  logger.info('🧹 Clearing Apollo Client cache...')
  
  try {
    if (clientRef && clientRef.cache) {
      // 清理Apollo Client的内存缓存
      await clientRef.cache.reset()
      logger.info('✅ Apollo Client cache cleared')
      return true
    } else {
      logger.warn('⚠️ Apollo Client not available for cache clearing')
      return false
    }
  } catch (error) {
    logger.error('❌ Failed to clear Apollo Client cache', {
      error: error.message
    })
    return false
  }
}

/**
 * 清理AsyncStorage缓存
 */
export async function clearAsyncStorage() {
  logger.info('🧹 Clearing AsyncStorage cache...')
  
  try {
    // 获取所有keys
    const keys = await AsyncStorage.getAllKeys()
    logger.debug('Found AsyncStorage keys', { keys, count: keys.length })
    
    // 清理所有非关键数据
    const keysToKeep = ['restaurantId'] // 保留餐厅ID
    const keysToRemove = keys.filter(key => !keysToKeep.includes(key))
    
    if (keysToRemove.length > 0) {
      await AsyncStorage.multiRemove(keysToRemove)
      logger.info('✅ AsyncStorage cache cleared', { 
        removedKeys: keysToRemove,
        keptKeys: keysToKeep 
      })
    } else {
      logger.info('ℹ️ No AsyncStorage keys to remove')
    }
    
    return true
  } catch (error) {
    logger.error('❌ Failed to clear AsyncStorage cache', {
      error: error.message
    })
    return false
  }
}

/**
 * 检查认证状态
 */
export async function checkAuthStatus() {
  logger.info('🔍 Checking authentication status...')
  
  try {
    const token = await SecureStore.getItemAsync('token')
    const restaurantId = await AsyncStorage.getItem('restaurantId')
    
    const authStatus = {
      hasToken: !!token,
      tokenLength: token ? token.length : 0,
      hasRestaurantId: !!restaurantId,
      restaurantId: restaurantId
    }
    
    logger.info('Authentication status', authStatus)
    
    return authStatus
  } catch (error) {
    logger.error('❌ Failed to check auth status', {
      error: error.message
    })
    return null
  }
}

/**
 * 重新初始化Apollo Client
 */
export async function reinitializeApolloClient() {
  logger.info('🔄 Reinitializing Apollo Client...')
  
  try {
    if (clientRef) {
      // 停止所有活跃的查询
      await clientRef.stop()
      
      // 清理缓存
      await clientRef.cache.reset()
      
      // 重新启动
      await clientRef.reFetchObservableQueries()
      
      logger.info('✅ Apollo Client reinitialized')
      return true
    } else {
      logger.warn('⚠️ Apollo Client not available for reinitialization')
      return false
    }
  } catch (error) {
    logger.error('❌ Failed to reinitialize Apollo Client', {
      error: error.message
    })
    return false
  }
}

/**
 * 清理所有缓存
 */
export async function clearAllCaches() {
  logger.info('🚀 Starting comprehensive cache clearing...')
  
  const results = {
    apollo: false,
    asyncStorage: false,
    authCheck: null,
    reinitialize: false
  }
  
  // 1. 检查认证状态
  results.authCheck = await checkAuthStatus()
  
  // 2. 清理Apollo Client缓存
  results.apollo = await clearApolloCache()
  
  // 3. 清理AsyncStorage缓存
  results.asyncStorage = await clearAsyncStorage()
  
  // 4. 重新初始化Apollo Client
  results.reinitialize = await reinitializeApolloClient()
  
  const success = results.apollo && results.asyncStorage && results.reinitialize
  
  logger.info('🎯 Cache clearing summary', {
    results,
    overallSuccess: success
  })
  
  if (success) {
    logger.info('✅ All caches cleared successfully!')
  } else {
    logger.warn('⚠️ Some cache clearing operations failed')
  }
  
  return results
}

/**
 * 强制刷新GraphQL schema
 */
export async function forceSchemaRefresh() {
  logger.info('🔄 Forcing GraphQL schema refresh...')
  
  try {
    if (clientRef) {
      // 清理所有查询缓存
      await clientRef.cache.reset()
      
      // 重新获取所有活跃查询
      await clientRef.reFetchObservableQueries()
      
      logger.info('✅ GraphQL schema refresh completed')
      return true
    } else {
      logger.warn('⚠️ Apollo Client not available for schema refresh')
      return false
    }
  } catch (error) {
    logger.error('❌ Failed to refresh GraphQL schema', {
      error: error.message
    })
    return false
  }
}

/**
 * 诊断缓存问题
 */
export async function diagnoseCacheIssues() {
  logger.info('🔍 Diagnosing cache issues...')
  
  const diagnosis = {
    apolloClientAvailable: !!clientRef,
    cacheSize: 0,
    authStatus: null,
    asyncStorageKeys: [],
    issues: []
  }
  
  try {
    // 检查Apollo Client
    if (clientRef && clientRef.cache) {
      const cacheData = clientRef.cache.extract()
      diagnosis.cacheSize = Object.keys(cacheData).length
    } else {
      diagnosis.issues.push('Apollo Client or cache not available')
    }
    
    // 检查认证
    diagnosis.authStatus = await checkAuthStatus()
    if (!diagnosis.authStatus?.hasToken) {
      diagnosis.issues.push('No authentication token found')
    }
    
    // 检查AsyncStorage
    diagnosis.asyncStorageKeys = await AsyncStorage.getAllKeys()
    
    logger.info('Cache diagnosis complete', diagnosis)
    
    return diagnosis
  } catch (error) {
    logger.error('❌ Failed to diagnose cache issues', {
      error: error.message
    })
    diagnosis.issues.push(`Diagnosis failed: ${error.message}`)
    return diagnosis
  }
}

export default {
  clearApolloCache,
  clearAsyncStorage,
  checkAuthStatus,
  reinitializeApolloClient,
  clearAllCaches,
  forceSchemaRefresh,
  diagnoseCacheIssues
}
