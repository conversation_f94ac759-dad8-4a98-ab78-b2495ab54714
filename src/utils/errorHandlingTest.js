/**
 * 错误处理测试工具
 * 用于验证Logger和Sentry集成是否正常工作
 */

import Logger from '../core/logger'
import * as Sentry from 'sentry-expo'

const logger = new Logger('ErrorHandlingTest')

/**
 * 测试Logger的各个级别
 */
export function testLoggerLevels() {
  console.log('🧪 Testing Logger levels...')
  
  logger.debug('This is a debug message', { testData: 'debug test' })
  logger.info('This is an info message', { testData: 'info test' })
  logger.warn('This is a warning message', { testData: 'warning test' })
  logger.error('This is an error message', { testData: 'error test' })
  
  console.log('✅ Logger level tests completed')
}

/**
 * 测试Sentry错误上报
 */
export function testSentryIntegration() {
  console.log('🧪 Testing Sentry integration...')
  
  try {
    // 创建一个测试错误
    const testError = new Error('Test error for Sentry integration')
    testError.stack = 'Test stack trace'
    
    // 添加上下文信息
    Sentry.setContext('test_context', {
      testType: 'error_handling_verification',
      timestamp: new Date().toISOString(),
      environment: __DEV__ ? 'development' : 'production'
    })
    
    // 发送到Sentry
    Sentry.captureException(testError)
    
    logger.info('Test error sent to Sentry', { 
      errorMessage: testError.message,
      sentryEnabled: true 
    })
    
    console.log('✅ Sentry integration test completed')
  } catch (error) {
    logger.error('Sentry integration test failed', { 
      error: error.message 
    })
    console.log('❌ Sentry integration test failed')
  }
}

/**
 * 测试GraphQL错误处理模拟
 */
export function testGraphQLErrorHandling() {
  console.log('🧪 Testing GraphQL error handling simulation...')
  
  // 模拟GraphQL错误
  const mockGraphQLError = {
    message: 'Test GraphQL error',
    graphQLErrors: [
      {
        message: 'Field "testField" is not defined',
        locations: [{ line: 1, column: 1 }],
        path: ['testField']
      }
    ],
    networkError: null
  }
  
  logger.error('Simulated GraphQL error', {
    error: mockGraphQLError.message,
    graphQLErrors: mockGraphQLError.graphQLErrors,
    networkError: mockGraphQLError.networkError
  })
  
  // 模拟网络错误
  const mockNetworkError = {
    message: 'Network request failed',
    networkError: {
      name: 'NetworkError',
      message: 'Failed to fetch',
      stack: 'NetworkError: Failed to fetch'
    },
    graphQLErrors: null
  }
  
  logger.error('Simulated network error', {
    error: mockNetworkError.message,
    networkError: mockNetworkError.networkError,
    graphQLErrors: mockNetworkError.graphQLErrors
  })
  
  console.log('✅ GraphQL error handling simulation completed')
}

/**
 * 运行所有错误处理测试
 */
export function runAllErrorHandlingTests() {
  console.log('🚀 Starting comprehensive error handling tests...')
  
  testLoggerLevels()
  testSentryIntegration()
  testGraphQLErrorHandling()
  
  console.log('🎉 All error handling tests completed!')
  console.log('📝 Check console output and Sentry dashboard for results')
}

/**
 * 测试订单操作错误处理
 */
export function testOrderOperationErrors() {
  console.log('🧪 Testing order operation error scenarios...')
  
  const testOrderId = 'test_order_12345'
  const testReason = 'MERCHANT_OUT_OF_STOCK'
  
  // 模拟取消订单错误
  logger.error('Cancel order operation failed', {
    orderId: testOrderId,
    reason: testReason,
    userAction: 'cancel_order',
    error: 'GraphQL error: Invalid reason enum value'
  })
  
  // 模拟接受订单错误
  logger.error('Accept order operation failed', {
    orderId: testOrderId,
    time: '30',
    userAction: 'accept_order',
    error: 'Network error: Connection timeout'
  })
  
  // 模拟订单完成错误
  logger.error('Order pickup operation failed', {
    orderId: testOrderId,
    userAction: 'pickup_order',
    error: 'GraphQL error: Order not found'
  })
  
  // 模拟静音错误
  logger.error('Mute ring operation failed', {
    orderId: testOrderId,
    userAction: 'mute_ring',
    error: 'GraphQL error: Permission denied'
  })
  
  console.log('✅ Order operation error tests completed')
}

export default {
  testLoggerLevels,
  testSentryIntegration,
  testGraphQLErrorHandling,
  testOrderOperationErrors,
  runAllErrorHandlingTests
}
