/**
 * 测试取消订单修复
 * 验证客户端定义与服务器端是否完全匹配
 */

import { gql } from '@apollo/client'
import { REFUND_REASON } from '../utilities/orderEnums'
import { cancelOrder as cancelOrderMutation } from '../apollo/mutations'
import Logger from '../core/logger'

const logger = new Logger('TestCancelOrderFix')

/**
 * 验证客户端与服务器端定义的一致性
 */
export function verifySchemaConsistency() {
  logger.info('🔍 Verifying schema consistency...')
  
  // 服务器端定义（从用户提供的信息）
  const serverSchema = {
    mutation: 'cancelOrder(_id: String!, reason: RefundReason!): Order!',
    enumValues: [
      'MERCHANT_OUT_OF_STOCK',
      'MERCHANT_CANNOT_DELIVER', 
      'MERCHANT_OTHER',
      'CUSTOMER_CANCELLED'
    ]
  }
  
  // 客户端定义
  const clientMutation = cancelOrderMutation
  const clientEnumValues = Object.values(REFUND_REASON)
  
  logger.info('Schema comparison', {
    server: serverSchema,
    client: {
      mutation: clientMutation,
      enumValues: clientEnumValues
    }
  })
  
  // 检查mutation定义
  const mutationMatches = clientMutation.includes('RefundReason!')
  
  // 检查枚举值
  const enumMatches = serverSchema.enumValues.every(value => 
    clientEnumValues.includes(value)
  )
  
  const allEnumValuesMatch = clientEnumValues.length === serverSchema.enumValues.length
  
  const result = {
    mutationMatches,
    enumMatches,
    allEnumValuesMatch,
    isConsistent: mutationMatches && enumMatches && allEnumValuesMatch
  }
  
  logger.info('Consistency check result', result)
  
  return result
}

/**
 * 测试枚举值的正确格式
 */
export function testEnumValueFormat() {
  logger.info('🔍 Testing enum value format...')
  
  const testValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  
  // 检查值的格式
  const checks = {
    isString: typeof testValue === 'string',
    isUpperCase: testValue === testValue.toUpperCase(),
    noSpaces: !testValue.includes(' '),
    noHyphens: !testValue.includes('-'),
    matchesKey: testValue === 'MERCHANT_OUT_OF_STOCK',
    length: testValue.length,
    charCodes: testValue.split('').map(c => c.charCodeAt(0))
  }
  
  logger.info('Enum value format checks', {
    value: testValue,
    checks,
    allChecksPass: Object.values(checks).slice(0, 5).every(Boolean)
  })
  
  return checks
}

/**
 * 创建正确的GraphQL请求
 */
export function createCorrectRequest(orderId = 'test_order_123') {
  logger.info('🔍 Creating correct GraphQL request...')
  
  try {
    // 解析mutation
    const mutation = gql`${cancelOrderMutation}`
    
    // 构造变量
    const variables = {
      _id: orderId,
      reason: REFUND_REASON.MERCHANT_OUT_OF_STOCK
    }
    
    // 创建请求对象
    const request = {
      query: mutation,
      variables: variables,
      operationName: 'CancelOrder'
    }
    
    logger.info('Request created successfully', {
      operationName: request.operationName,
      variables: request.variables,
      hasQuery: !!request.query
    })
    
    // 验证请求格式
    const validation = {
      hasValidOrderId: typeof variables._id === 'string' && variables._id.length > 0,
      hasValidReason: typeof variables.reason === 'string' && 
                     Object.values(REFUND_REASON).includes(variables.reason),
      reasonIsEnum: variables.reason === 'MERCHANT_OUT_OF_STOCK'
    }
    
    logger.info('Request validation', validation)
    
    return { request, validation }
  } catch (error) {
    logger.error('Failed to create request', {
      error: error.message,
      stack: error.stack
    })
    return { error }
  }
}

/**
 * 模拟Apollo Client发送过程
 */
export function simulateApolloSend(orderId = 'test_order_123') {
  logger.info('🔍 Simulating Apollo Client send process...')
  
  const { request, validation, error } = createCorrectRequest(orderId)
  
  if (error) {
    logger.error('Cannot simulate - request creation failed')
    return { error }
  }
  
  // 模拟Apollo Client的序列化
  const serializedVariables = JSON.stringify(request.variables)
  const parsedVariables = JSON.parse(serializedVariables)
  
  logger.info('Serialization test', {
    original: request.variables,
    serialized: serializedVariables,
    parsed: parsedVariables,
    reasonPreserved: parsedVariables.reason === request.variables.reason
  })
  
  // 模拟网络请求格式
  const networkRequest = {
    operationName: request.operationName,
    variables: request.variables,
    query: request.query.loc.source.body
  }
  
  logger.info('Network request format', {
    operationName: networkRequest.operationName,
    variables: networkRequest.variables,
    queryLength: networkRequest.query.length,
    queryIncludesRefundReason: networkRequest.query.includes('RefundReason!')
  })
  
  return { networkRequest, validation }
}

/**
 * 检查可能的问题
 */
export function checkPotentialProblems() {
  logger.info('🔍 Checking potential problems...')
  
  const problems = []
  
  // 检查1: 枚举值定义
  const enumValue = REFUND_REASON.MERCHANT_OUT_OF_STOCK
  if (enumValue !== 'MERCHANT_OUT_OF_STOCK') {
    problems.push(`Enum value mismatch: expected 'MERCHANT_OUT_OF_STOCK', got '${enumValue}'`)
  }
  
  // 检查2: mutation字符串
  if (!cancelOrderMutation.includes('RefundReason!')) {
    problems.push('Mutation does not include RefundReason! type')
  }
  
  // 检查3: 变量名
  if (!cancelOrderMutation.includes('$reason')) {
    problems.push('Mutation does not include $reason variable')
  }
  
  // 检查4: 操作名
  if (!cancelOrderMutation.includes('CancelOrder')) {
    problems.push('Mutation does not include CancelOrder operation name')
  }
  
  logger.info('Problem check results', {
    problems,
    problemCount: problems.length,
    hasProblems: problems.length > 0
  })
  
  return problems
}

/**
 * 运行完整的修复验证
 */
export function runFixVerification(orderId = 'test_order_123') {
  logger.info('🚀 Running complete fix verification...')
  
  const schemaCheck = verifySchemaConsistency()
  const formatCheck = testEnumValueFormat()
  const requestTest = createCorrectRequest(orderId)
  const simulationTest = simulateApolloSend(orderId)
  const problemCheck = checkPotentialProblems()
  
  const summary = {
    schemaConsistent: schemaCheck.isConsistent,
    enumFormatCorrect: formatCheck.allChecksPass,
    requestCreated: !requestTest.error,
    simulationSuccessful: !simulationTest.error,
    problemsFound: problemCheck.length,
    overallStatus: schemaCheck.isConsistent && 
                   formatCheck.allChecksPass && 
                   !requestTest.error && 
                   !simulationTest.error && 
                   problemCheck.length === 0
  }
  
  logger.info('🎯 Fix verification summary', summary)
  
  if (summary.overallStatus) {
    logger.info('✅ All verifications passed - the fix should work!')
  } else {
    logger.warn('⚠️ Some verifications failed - issues remain', {
      schemaIssue: !schemaCheck.isConsistent,
      formatIssue: !formatCheck.allChecksPass,
      requestIssue: !!requestTest.error,
      simulationIssue: !!simulationTest.error,
      problems: problemCheck
    })
  }
  
  return summary
}

export default {
  verifySchemaConsistency,
  testEnumValueFormat,
  createCorrectRequest,
  simulateApolloSend,
  checkPotentialProblems,
  runFixVerification
}
